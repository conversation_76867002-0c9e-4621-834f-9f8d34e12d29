<?php

namespace App\Console\Commands;

use App\Services\OpenAIRealtimeService;
use Illuminate\Console\Command;

class TestRealtimeCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'openai:test-realtime {message?}';

    /**
     * The console command description.
     */
    protected $description = 'Test OpenAI Realtime API WebSocket connection';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $message = $this->argument('message') ?? 'Merhaba, nasılsın?';
        
        $this->info('Testing OpenAI Realtime API WebSocket connection...');
        $this->info("Message to send: {$message}");
        
        $realtimeService = new OpenAIRealtimeService();
        
        // Set up event handlers
        $realtimeService
            ->onConnect(function () {
                $this->info('✅ Connected to OpenAI Realtime API');
            })
            ->onMessage(function ($message) {
                $type = $message['type'] ?? 'unknown';
                $this->line("📨 Received: {$type}");
                
                // Handle specific message types
                switch ($type) {
                    case 'session.created':
                        $this->info('🎯 Session created successfully');
                        break;
                        
                    case 'response.text.delta':
                        if (isset($message['delta'])) {
                            $this->line("💬 Text: " . $message['delta']);
                        }
                        break;
                        
                    case 'response.audio.delta':
                        if (isset($message['delta'])) {
                            $this->line("🔊 Audio delta received (length: " . strlen($message['delta']) . ")");
                        }
                        break;
                        
                    case 'response.done':
                        $this->info('✅ Response completed');
                        break;
                        
                    case 'error':
                        $error = $message['error'] ?? 'Unknown error';
                        $this->error("❌ Error: " . json_encode($error));
                        break;
                }
            })
            ->onError(function ($error) {
                $this->error("❌ Connection error: " . $error->getMessage());
            })
            ->onDisconnect(function () {
                $this->info('🔌 Disconnected from OpenAI Realtime API');
            });

        // Connect to OpenAI
        $realtimeService->connect()
            ->then(function () use ($realtimeService, $message) {
                $this->info('🚀 Sending message...');
                
                // Wait a moment for session to be ready
                sleep(2);

                $success = $realtimeService->sendTextMessage($message);

                if ($success) {
                    $this->info('📤 Message sent successfully');
                } else {
                    $this->error('❌ Failed to send message');
                }

                // Wait for response
                $this->info('⏳ Waiting for response...');
                sleep(10);

                $this->info('⏰ Timeout reached, disconnecting...');
                $realtimeService->disconnect();
            })
            ->otherwise(function ($error) {
                $this->error("❌ Failed to connect: " . $error->getMessage());
                return;
            });

        $this->info('✅ Test completed');
    }
}
